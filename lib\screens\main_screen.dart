import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/category_provider.dart';
import '../providers/account_provider.dart';
import '../constants/app_theme.dart';
import 'category_screen.dart';
import 'add_account_screen.dart';
import 'settings_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _initializeTabController();
  }

  void _initializeTabController() {
    final categories = context.read<CategoryProvider>().categories;
    _tabController = TabController(length: categories.length, vsync: this);
    // Tab controller listener can be added here if needed
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _navigateToAddAccount() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const AddAccountScreen()));
  }

  void _navigateToSettings() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const SettingsScreen()));
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CategoryProvider>(
      builder: (context, categoryProvider, child) {
        if (categoryProvider.isLoading) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (categoryProvider.error != null) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
                  const SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل البيانات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    categoryProvider.error!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      categoryProvider.loadCategories();
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            ),
          );
        }

        final categories = categoryProvider.categories;
        if (categories.isEmpty) {
          return const Scaffold(
            body: Center(child: Text('لا توجد فئات متاحة')),
          );
        }

        // Reinitialize tab controller if categories changed
        if (_tabController.length != categories.length) {
          _tabController.dispose();
          _tabController = TabController(
            length: categories.length,
            vsync: this,
          );
          // Tab controller listener can be added here if needed
        }

        return Scaffold(
          appBar: AppBar(
            title: const Text('دليل الحسابات'),
            actions: [
              IconButton(
                icon: const Icon(Icons.settings),
                onPressed: _navigateToSettings,
                tooltip: 'الإعدادات',
              ),
              IconButton(
                icon: const Icon(Icons.search),
                onPressed: () {
                  // TODO: Implement search functionality
                },
                tooltip: 'البحث',
              ),
            ],
            bottom: TabBar(
              controller: _tabController,
              isScrollable: true,
              tabs:
                  categories.map((category) {
                    return Tab(text: category.name);
                  }).toList(),
            ),
          ),
          body: TabBarView(
            controller: _tabController,
            children:
                categories.map((category) {
                  return CategoryScreen(category: category);
                }).toList(),
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: _navigateToAddAccount,
            tooltip: 'إضافة حساب جديد',
            child: const Icon(Icons.add),
          ),
          drawer: _buildDrawer(context),
        );
      },
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: const BoxDecoration(color: AppTheme.primaryPurple),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(
                  Icons.account_balance_wallet,
                  size: 48,
                  color: Colors.white,
                ),
                const SizedBox(height: 16),
                const Text(
                  'دليل الحسابات',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                  ),
                ),
                const SizedBox(height: 4),
                Consumer<AccountProvider>(
                  builder: (context, accountProvider, child) {
                    final summary = accountProvider.getOverallSummary();
                    return Text(
                      'إجمالي الرصيد: ${summary['balance']?.toStringAsFixed(2) ?? '0.00'}',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                        fontFamily: 'Cairo',
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.home),
            title: const Text('الصفحة الرئيسية'),
            onTap: () {
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.add),
            title: const Text('إضافة حساب جديد'),
            onTap: () {
              Navigator.pop(context);
              _navigateToAddAccount();
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('الإعدادات'),
            onTap: () {
              Navigator.pop(context);
              _navigateToSettings();
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.assessment),
            title: const Text('التقارير'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to reports screen
            },
          ),
          ListTile(
            leading: const Icon(Icons.backup),
            title: const Text('النسخ الاحتياطي'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Implement backup functionality
            },
          ),
          ListTile(
            leading: const Icon(Icons.info),
            title: const Text('حول التطبيق'),
            onTap: () {
              Navigator.pop(context);
              _showAboutDialog(context);
            },
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'دليل الحسابات',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(
        Icons.account_balance_wallet,
        size: 48,
        color: AppTheme.primaryPurple,
      ),
      children: [
        const Text(
          'تطبيق إدارة الديون والحسابات الشخصية',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
        const SizedBox(height: 16),
        const Text(
          'يساعدك هذا التطبيق على تتبع الديون والحسابات بطريقة منظمة وسهلة.',
          style: TextStyle(fontFamily: 'Cairo'),
        ),
      ],
    );
  }
}
