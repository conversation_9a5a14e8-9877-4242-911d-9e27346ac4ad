import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/category.dart';
import '../models/currency.dart';
import '../models/account.dart';
import '../providers/account_provider.dart';
import '../constants/app_theme.dart';
import '../widgets/account_card.dart';
import '../widgets/summary_card.dart';
import 'add_account_screen.dart';

class CurrencyAccountsScreen extends StatefulWidget {
  final Category category;
  final Currency currency;

  const CurrencyAccountsScreen({
    super.key,
    required this.category,
    required this.currency,
  });

  @override
  State<CurrencyAccountsScreen> createState() => _CurrencyAccountsScreenState();
}

class _CurrencyAccountsScreenState extends State<CurrencyAccountsScreen> {
  String _searchQuery = '';
  DebtType? _filterDebtType;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(widget.currency.name),
            Text(
              widget.category.name,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuSelection,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'filter',
                child: Text('تصفية'),
              ),
              const PopupMenuItem(
                value: 'sort',
                child: Text('ترتيب'),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Text('تصدير'),
              ),
            ],
          ),
        ],
      ),
      body: Consumer<AccountProvider>(
        builder: (context, accountProvider, child) {
          final allAccounts = accountProvider.getAccountsByCategoryAndCurrency(
            widget.category.id!,
            widget.currency.id!,
          );

          final filteredAccounts = _filterAccounts(allAccounts);

          return RefreshIndicator(
            onRefresh: () async {
              await accountProvider.loadAccounts();
            },
            child: Column(
              children: [
                // Summary Card
                SummaryCard(
                  accounts: filteredAccounts,
                  currency: widget.currency,
                ),
                
                // Filter chips
                if (_filterDebtType != null || _searchQuery.isNotEmpty)
                  _buildFilterChips(),
                
                // Accounts List
                Expanded(
                  child: filteredAccounts.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: filteredAccounts.length,
                          itemBuilder: (context, index) {
                            final account = filteredAccounts[index];
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: AccountCard(
                                account: account,
                                currency: widget.currency,
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => AddAccountScreen(
                preselectedCategory: widget.category,
                preselectedCurrency: widget.currency,
              ),
            ),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  List<Account> _filterAccounts(List<Account> accounts) {
    var filtered = accounts;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((account) {
        return account.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               account.phone.contains(_searchQuery) ||
               account.details.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Apply debt type filter
    if (_filterDebtType != null) {
      filtered = filtered.where((account) => account.debtType == _filterDebtType).toList();
    }

    return filtered;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty || _filterDebtType != null
                ? 'لا توجد نتائج للبحث'
                : 'لا توجد حسابات',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _filterDebtType != null
                ? 'جرب تغيير معايير البحث'
                : 'اضغط على زر + لإضافة حساب جديد',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Wrap(
        spacing: 8,
        children: [
          if (_searchQuery.isNotEmpty)
            Chip(
              label: Text('البحث: $_searchQuery'),
              onDeleted: () {
                setState(() {
                  _searchQuery = '';
                });
              },
            ),
          if (_filterDebtType != null)
            Chip(
              label: Text(_filterDebtType!.arabicName),
              onDeleted: () {
                setState(() {
                  _filterDebtType = null;
                });
              },
            ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        String tempQuery = _searchQuery;
        return AlertDialog(
          title: const Text('البحث'),
          content: TextField(
            onChanged: (value) {
              tempQuery = value;
            },
            decoration: const InputDecoration(
              hintText: 'ابحث في الأسماء أو الأرقام أو التفاصيل',
              prefixIcon: Icon(Icons.search),
            ),
            controller: TextEditingController(text: _searchQuery),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _searchQuery = tempQuery;
                });
                Navigator.of(context).pop();
              },
              child: const Text('بحث'),
            ),
          ],
        );
      },
    );
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'filter':
        _showFilterDialog();
        break;
      case 'sort':
        _showSortDialog();
        break;
      case 'export':
        _exportData();
        break;
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        DebtType? tempFilter = _filterDebtType;
        return AlertDialog(
          title: const Text('تصفية الحسابات'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RadioListTile<DebtType?>(
                title: const Text('جميع الحسابات'),
                value: null,
                groupValue: tempFilter,
                onChanged: (value) {
                  tempFilter = value;
                },
              ),
              RadioListTile<DebtType?>(
                title: const Text('له (مدين لي)'),
                value: DebtType.owedToMe,
                groupValue: tempFilter,
                onChanged: (value) {
                  tempFilter = value;
                },
              ),
              RadioListTile<DebtType?>(
                title: const Text('عليه (أنا مدين)'),
                value: DebtType.iOwe,
                groupValue: tempFilter,
                onChanged: (value) {
                  tempFilter = value;
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _filterDebtType = tempFilter;
                });
                Navigator.of(context).pop();
              },
              child: const Text('تطبيق'),
            ),
          ],
        );
      },
    );
  }

  void _showSortDialog() {
    // TODO: Implement sort functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة خاصية الترتيب قريباً')),
    );
  }

  void _exportData() {
    // TODO: Implement export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة خاصية التصدير قريباً')),
    );
  }
}
