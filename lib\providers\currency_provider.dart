import 'package:flutter/foundation.dart';
import '../models/currency.dart';
import '../database/database_helper.dart';

class CurrencyProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Currency> _currencies = [];
  bool _isLoading = false;
  String? _error;

  List<Currency> get currencies => _currencies;
  bool get isLoading => _isLoading;
  String? get error => _error;

  CurrencyProvider() {
    loadCurrencies();
  }

  Future<void> loadCurrencies() async {
    _setLoading(true);
    try {
      _currencies = await _databaseHelper.getCurrencies();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل العملات: ${e.toString()}';
      debugPrint('Error loading currencies: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> addCurrency(String name, String symbol) async {
    if (name.trim().isEmpty) {
      _error = 'اسم العملة مطلوب';
      notifyListeners();
      return false;
    }

    if (symbol.trim().isEmpty) {
      _error = 'رمز العملة مطلوب';
      notifyListeners();
      return false;
    }

    // Check if currency already exists
    if (_currencies.any(
      (currency) =>
          currency.name.toLowerCase() == name.toLowerCase() ||
          currency.symbol == symbol.trim(),
    )) {
      _error = 'هذه العملة أو رمزها موجود بالفعل';
      notifyListeners();
      return false;
    }

    _setLoading(true);
    try {
      final currency = Currency(
        name: name.trim(),
        symbol: symbol.trim(),
        createdAt: DateTime.now(),
      );

      final id = await _databaseHelper.insertCurrency(currency);
      if (id > 0) {
        await loadCurrencies(); // Reload to get the new currency with ID
        _error = null;
        return true;
      } else {
        _error = 'فشل في إضافة العملة';
        return false;
      }
    } catch (e) {
      _error = 'خطأ في إضافة العملة: ${e.toString()}';
      debugPrint('Error adding currency: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateCurrency(int id, String name, String symbol) async {
    if (name.trim().isEmpty) {
      _error = 'اسم العملة مطلوب';
      notifyListeners();
      return false;
    }

    if (symbol.trim().isEmpty) {
      _error = 'رمز العملة مطلوب';
      notifyListeners();
      return false;
    }

    // Check if another currency with the same name or symbol exists
    if (_currencies.any(
      (currency) =>
          currency.id != id &&
          (currency.name.toLowerCase() == name.toLowerCase() ||
              currency.symbol == symbol.trim()),
    )) {
      _error = 'هذه العملة أو رمزها موجود بالفعل';
      notifyListeners();
      return false;
    }

    _setLoading(true);
    try {
      final existingCurrency = _currencies.firstWhere(
        (currency) => currency.id == id,
      );
      final updatedCurrency = existingCurrency.copyWith(
        name: name.trim(),
        symbol: symbol.trim(),
      );

      final result = await _databaseHelper.updateCurrency(updatedCurrency);
      if (result > 0) {
        await loadCurrencies(); // Reload to reflect changes
        _error = null;
        return true;
      } else {
        _error = 'فشل في تحديث العملة';
        return false;
      }
    } catch (e) {
      _error = 'خطأ في تحديث العملة: ${e.toString()}';
      debugPrint('Error updating currency: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteCurrency(int id) async {
    // Don't allow deletion of default currencies
    final currency = _currencies.firstWhere((curr) => curr.id == id);
    final defaultCurrencies = [
      'ريال سعودي',
      'دولار أمريكي',
      'يورو',
      'جنيه مصري',
    ];

    if (defaultCurrencies.contains(currency.name)) {
      _error = 'لا يمكن حذف العملات الافتراضية';
      notifyListeners();
      return false;
    }

    _setLoading(true);
    try {
      final result = await _databaseHelper.deleteCurrency(id);
      if (result > 0) {
        await loadCurrencies(); // Reload to reflect changes
        _error = null;
        return true;
      } else {
        _error = 'فشل في حذف العملة';
        return false;
      }
    } catch (e) {
      _error = 'خطأ في حذف العملة: ${e.toString()}';
      debugPrint('Error deleting currency: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Currency? getCurrencyById(int id) {
    try {
      return _currencies.firstWhere((currency) => currency.id == id);
    } catch (e) {
      return null;
    }
  }

  Currency? getCurrencyByName(String name) {
    try {
      return _currencies.firstWhere(
        (currency) => currency.name.toLowerCase() == name.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  Currency? get defaultCurrency {
    return getCurrencyByName('ريال سعودي');
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
