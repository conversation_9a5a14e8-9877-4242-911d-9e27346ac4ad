import 'package:flutter/foundation.dart' hide Category;
import '../models/category.dart';
import '../database/database_helper.dart';

class CategoryProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Category> _categories = [];
  bool _isLoading = false;
  String? _error;

  List<Category> get categories => _categories;
  bool get isLoading => _isLoading;
  String? get error => _error;

  CategoryProvider() {
    loadCategories();
  }

  Future<void> loadCategories() async {
    _setLoading(true);
    try {
      _categories = await _databaseHelper.getCategories();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الفئات: ${e.toString()}';
      debugPrint('Error loading categories: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> addCategory(String name) async {
    if (name.trim().isEmpty) {
      _error = 'اسم الفئة مطلوب';
      notifyListeners();
      return false;
    }

    // Check if category already exists
    if (_categories.any(
      (category) => category.name.toLowerCase() == name.toLowerCase(),
    )) {
      _error = 'هذه الفئة موجودة بالفعل';
      notifyListeners();
      return false;
    }

    _setLoading(true);
    try {
      final category = Category(name: name.trim(), createdAt: DateTime.now());

      final id = await _databaseHelper.insertCategory(category);
      if (id > 0) {
        await loadCategories(); // Reload to get the new category with ID
        _error = null;
        return true;
      } else {
        _error = 'فشل في إضافة الفئة';
        return false;
      }
    } catch (e) {
      _error = 'خطأ في إضافة الفئة: ${e.toString()}';
      debugPrint('Error adding category: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateCategory(int id, String name) async {
    if (name.trim().isEmpty) {
      _error = 'اسم الفئة مطلوب';
      notifyListeners();
      return false;
    }

    // Check if another category with the same name exists
    if (_categories.any(
      (category) =>
          category.id != id &&
          category.name.toLowerCase() == name.toLowerCase(),
    )) {
      _error = 'هذه الفئة موجودة بالفعل';
      notifyListeners();
      return false;
    }

    _setLoading(true);
    try {
      final existingCategory = _categories.firstWhere(
        (category) => category.id == id,
      );
      final updatedCategory = existingCategory.copyWith(name: name.trim());

      final result = await _databaseHelper.updateCategory(updatedCategory);
      if (result > 0) {
        await loadCategories(); // Reload to reflect changes
        _error = null;
        return true;
      } else {
        _error = 'فشل في تحديث الفئة';
        return false;
      }
    } catch (e) {
      _error = 'خطأ في تحديث الفئة: ${e.toString()}';
      debugPrint('Error updating category: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteCategory(int id) async {
    // Don't allow deletion of the default "عام" category
    final category = _categories.firstWhere((cat) => cat.id == id);
    if (category.name == 'عام') {
      _error = 'لا يمكن حذف الفئة الافتراضية';
      notifyListeners();
      return false;
    }

    _setLoading(true);
    try {
      final result = await _databaseHelper.deleteCategory(id);
      if (result > 0) {
        await loadCategories(); // Reload to reflect changes
        _error = null;
        return true;
      } else {
        _error = 'فشل في حذف الفئة';
        return false;
      }
    } catch (e) {
      _error = 'خطأ في حذف الفئة: ${e.toString()}';
      debugPrint('Error deleting category: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Category? getCategoryById(int id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  Category? getCategoryByName(String name) {
    try {
      return _categories.firstWhere(
        (category) => category.name.toLowerCase() == name.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  Category? get defaultCategory {
    return getCategoryByName('عام');
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
