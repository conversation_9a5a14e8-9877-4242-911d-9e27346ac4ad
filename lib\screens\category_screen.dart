import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/category.dart';
import '../models/currency.dart';
import '../models/account.dart';
import '../providers/account_provider.dart';
import '../providers/currency_provider.dart';
import '../constants/app_theme.dart';
import '../widgets/account_card.dart';
import '../widgets/summary_card.dart';
import 'currency_accounts_screen.dart';

class CategoryScreen extends StatefulWidget {
  final Category category;

  const CategoryScreen({
    super.key,
    required this.category,
  });

  @override
  State<CategoryScreen> createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Consumer2<AccountProvider, CurrencyProvider>(
      builder: (context, accountProvider, currencyProvider, child) {
        final categoryAccounts = accountProvider.getAccountsByCategory(widget.category.id!);
        final currencies = currencyProvider.currencies;
        
        // Group accounts by currency
        final Map<Currency, List<Account>> accountsByCurrency = {};
        for (final account in categoryAccounts) {
          final currency = currencyProvider.getCurrencyById(account.currencyId);
          if (currency != null) {
            accountsByCurrency.putIfAbsent(currency, () => []).add(account);
          }
        }

        if (accountsByCurrency.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () async {
            await accountProvider.loadAccounts();
          },
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Category Summary
              _buildCategorySummary(accountProvider),
              const SizedBox(height: 16),
              
              // Currency sections
              ...accountsByCurrency.entries.map((entry) {
                final currency = entry.key;
                final accounts = entry.value;
                return _buildCurrencySection(currency, accounts);
              }).toList(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد حسابات في فئة "${widget.category.name}"',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على زر + لإضافة حساب جديد',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySummary(AccountProvider accountProvider) {
    final summary = accountProvider.getCategorySummary(widget.category.id!);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص فئة "${widget.category.name}"',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'لي',
                    summary['owedToMe'] ?? 0.0,
                    AppTheme.greenCredit,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSummaryItem(
                    'علي',
                    summary['iOwe'] ?? 0.0,
                    AppTheme.redDebt,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSummaryItem(
                    'الرصيد',
                    summary['balance'] ?? 0.0,
                    (summary['balance'] ?? 0.0) >= 0 
                        ? AppTheme.greenCredit 
                        : AppTheme.redDebt,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, double amount, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          amount.toStringAsFixed(2),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildCurrencySection(Currency currency, List<Account> accounts) {
    final summary = _calculateCurrencySummary(accounts);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Currency header with summary
        Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: InkWell(
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => CurrencyAccountsScreen(
                    category: widget.category,
                    currency: currency,
                  ),
                ),
              );
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryPurple.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        currency.symbol,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryPurple,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          currency.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          '${accounts.length} حساب',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${summary['balance']?.toStringAsFixed(2)} ${currency.symbol}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: (summary['balance'] ?? 0.0) >= 0 
                              ? AppTheme.greenCredit 
                              : AppTheme.redDebt,
                        ),
                      ),
                      Text(
                        'الرصيد الصافي',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey,
                  ),
                ],
              ),
            ),
          ),
        ),
        
        // Show first few accounts as preview
        ...accounts.take(3).map((account) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: AccountCard(
              account: account,
              currency: currency,
              isPreview: true,
            ),
          );
        }).toList(),
        
        // Show more button if there are more accounts
        if (accounts.length > 3)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Center(
              child: TextButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => CurrencyAccountsScreen(
                        category: widget.category,
                        currency: currency,
                      ),
                    ),
                  );
                },
                child: Text('عرض جميع الحسابات (${accounts.length})'),
              ),
            ),
          ),
        
        const SizedBox(height: 16),
      ],
    );
  }

  Map<String, double> _calculateCurrencySummary(List<Account> accounts) {
    double owedToMe = 0.0;
    double iOwe = 0.0;

    for (final account in accounts) {
      if (account.debtType == DebtType.owedToMe) {
        owedToMe += account.amount;
      } else {
        iOwe += account.amount;
      }
    }

    return {
      'owedToMe': owedToMe,
      'iOwe': iOwe,
      'balance': owedToMe - iOwe,
    };
  }
}
