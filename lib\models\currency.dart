class Currency {
  final int? id;
  final String name;
  final String symbol;
  final DateTime createdAt;

  Currency({
    this.id,
    required this.name,
    required this.symbol,
    required this.createdAt,
  });

  // Convert Currency to Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'symbol': symbol,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // Create Currency from Map (database result)
  factory Currency.fromMap(Map<String, dynamic> map) {
    return Currency(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      symbol: map['symbol'] ?? '',
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  // Create a copy of Currency with updated fields
  Currency copyWith({
    int? id,
    String? name,
    String? symbol,
    DateTime? createdAt,
  }) {
    return Currency(
      id: id ?? this.id,
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Currency{id: $id, name: $name, symbol: $symbol, createdAt: $createdAt}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Currency &&
        other.id == id &&
        other.name == name &&
        other.symbol == symbol &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ symbol.hashCode ^ createdAt.hashCode;
  }
}
