enum DebtType {
  owedToMe, // له (owed to me)
  iOwe,     // عليه (I owe)
}

class Account {
  final int? id;
  final String name;
  final String phone;
  final double amount;
  final String details;
  final int currencyId;
  final int categoryId;
  final DebtType debtType;
  final DateTime date;
  final DateTime createdAt;

  Account({
    this.id,
    required this.name,
    required this.phone,
    required this.amount,
    required this.details,
    required this.currencyId,
    required this.categoryId,
    required this.debtType,
    required this.date,
    required this.createdAt,
  });

  // Convert Account to Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'amount': amount,
      'details': details,
      'currency_id': currencyId,
      'category_id': categoryId,
      'debt_type': debtType == DebtType.owedToMe ? 'owed_to_me' : 'i_owe',
      'date': date.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  // Create Account from Map (database result)
  factory Account.fromMap(Map<String, dynamic> map) {
    return Account(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      phone: map['phone'] ?? '',
      amount: map['amount']?.toDouble() ?? 0.0,
      details: map['details'] ?? '',
      currencyId: map['currency_id']?.toInt() ?? 0,
      categoryId: map['category_id']?.toInt() ?? 0,
      debtType: map['debt_type'] == 'owed_to_me' 
          ? DebtType.owedToMe 
          : DebtType.iOwe,
      date: DateTime.parse(map['date']),
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  // Create a copy of Account with updated fields
  Account copyWith({
    int? id,
    String? name,
    String? phone,
    double? amount,
    String? details,
    int? currencyId,
    int? categoryId,
    DebtType? debtType,
    DateTime? date,
    DateTime? createdAt,
  }) {
    return Account(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      amount: amount ?? this.amount,
      details: details ?? this.details,
      currencyId: currencyId ?? this.currencyId,
      categoryId: categoryId ?? this.categoryId,
      debtType: debtType ?? this.debtType,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Account{id: $id, name: $name, phone: $phone, amount: $amount, '
           'details: $details, currencyId: $currencyId, categoryId: $categoryId, '
           'debtType: $debtType, date: $date, createdAt: $createdAt}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Account &&
        other.id == id &&
        other.name == name &&
        other.phone == phone &&
        other.amount == amount &&
        other.details == details &&
        other.currencyId == currencyId &&
        other.categoryId == categoryId &&
        other.debtType == debtType &&
        other.date == date &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        phone.hashCode ^
        amount.hashCode ^
        details.hashCode ^
        currencyId.hashCode ^
        categoryId.hashCode ^
        debtType.hashCode ^
        date.hashCode ^
        createdAt.hashCode;
  }
}

// Helper extension for DebtType
extension DebtTypeExtension on DebtType {
  String get arabicName {
    switch (this) {
      case DebtType.owedToMe:
        return 'له';
      case DebtType.iOwe:
        return 'عليه';
    }
  }

  String get englishName {
    switch (this) {
      case DebtType.owedToMe:
        return 'Owed to me';
      case DebtType.iOwe:
        return 'I owe';
    }
  }
}
