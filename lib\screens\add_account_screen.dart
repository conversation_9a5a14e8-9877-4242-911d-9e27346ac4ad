import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/account.dart';
import '../models/category.dart';
import '../models/currency.dart';
import '../providers/account_provider.dart';
import '../providers/category_provider.dart';
import '../providers/currency_provider.dart';
import '../constants/app_theme.dart';

class AddAccountScreen extends StatefulWidget {
  final Account? account; // For editing existing account
  final Category? preselectedCategory;
  final Currency? preselectedCurrency;

  const AddAccountScreen({
    super.key,
    this.account,
    this.preselectedCategory,
    this.preselectedCurrency,
  });

  @override
  State<AddAccountScreen> createState() => _AddAccountScreenState();
}

class _AddAccountScreenState extends State<AddAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _amountController = TextEditingController();
  final _detailsController = TextEditingController();

  Category? _selectedCategory;
  Currency? _selectedCurrency;
  DebtType _selectedDebtType = DebtType.owedToMe;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.account != null) {
      // Editing existing account
      final account = widget.account!;
      _nameController.text = account.name;
      _phoneController.text = account.phone;
      _amountController.text = account.amount.toString();
      _detailsController.text = account.details;
      _selectedDebtType = account.debtType;
      _selectedDate = account.date;

      // Set category and currency from providers
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final categoryProvider = context.read<CategoryProvider>();
        final currencyProvider = context.read<CurrencyProvider>();

        setState(() {
          _selectedCategory = categoryProvider.getCategoryById(
            account.categoryId,
          );
          _selectedCurrency = currencyProvider.getCurrencyById(
            account.currencyId,
          );
        });
      });
    } else {
      // Adding new account
      _selectedCategory = widget.preselectedCategory;
      _selectedCurrency = widget.preselectedCurrency;

      // Set default category and currency if not preselected
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final categoryProvider = context.read<CategoryProvider>();
        final currencyProvider = context.read<CurrencyProvider>();

        setState(() {
          _selectedCategory ??= categoryProvider.defaultCategory;
          _selectedCurrency ??= currencyProvider.defaultCurrency;
        });
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _amountController.dispose();
    _detailsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.account != null ? 'تعديل الحساب' : 'إضافة حساب جديد',
        ),
        actions: [
          if (widget.account != null)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _showDeleteConfirmation,
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Account Name
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم الحساب *',
                hintText: 'أدخل اسم الشخص أو الجهة',
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'اسم الحساب مطلوب';
                }
                return null;
              },
              textInputAction: TextInputAction.next,
            ),
            const SizedBox(height: 16),

            // Phone Number
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف',
                hintText: 'أدخل رقم الهاتف',
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
              textInputAction: TextInputAction.next,
            ),
            const SizedBox(height: 16),

            // Amount
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'المبلغ *',
                hintText: 'أدخل المبلغ',
                prefixIcon: Icon(Icons.attach_money),
              ),
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'المبلغ مطلوب';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'يجب أن يكون المبلغ أكبر من صفر';
                }
                return null;
              },
              textInputAction: TextInputAction.next,
            ),
            const SizedBox(height: 16),

            // Category Dropdown
            Consumer<CategoryProvider>(
              builder: (context, categoryProvider, child) {
                return DropdownButtonFormField<Category>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'الفئة *',
                    prefixIcon: Icon(Icons.category),
                  ),
                  items:
                      categoryProvider.categories
                          .map<DropdownMenuItem<Category>>((category) {
                            return DropdownMenuItem<Category>(
                              value: category,
                              child: Text(category.name),
                            );
                          })
                          .toList(),
                  onChanged: (category) {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'الفئة مطلوبة';
                    }
                    return null;
                  },
                );
              },
            ),
            const SizedBox(height: 16),

            // Currency Dropdown
            Consumer<CurrencyProvider>(
              builder: (context, currencyProvider, child) {
                return DropdownButtonFormField<Currency>(
                  value: _selectedCurrency,
                  decoration: const InputDecoration(
                    labelText: 'العملة *',
                    prefixIcon: Icon(Icons.monetization_on),
                  ),
                  items:
                      currencyProvider.currencies.map((currency) {
                        return DropdownMenuItem(
                          value: currency,
                          child: Text('${currency.name} (${currency.symbol})'),
                        );
                      }).toList(),
                  onChanged: (currency) {
                    setState(() {
                      _selectedCurrency = currency;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'العملة مطلوبة';
                    }
                    return null;
                  },
                );
              },
            ),
            const SizedBox(height: 16),

            // Debt Type
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'نوع المعاملة',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: RadioListTile<DebtType>(
                            title: const Text('له'),
                            subtitle: const Text('مدين لي'),
                            value: DebtType.owedToMe,
                            groupValue: _selectedDebtType,
                            onChanged: (value) {
                              setState(() {
                                _selectedDebtType = value!;
                              });
                            },
                            activeColor: AppTheme.greenCredit,
                          ),
                        ),
                        Expanded(
                          child: RadioListTile<DebtType>(
                            title: const Text('عليه'),
                            subtitle: const Text('أنا مدين'),
                            value: DebtType.iOwe,
                            groupValue: _selectedDebtType,
                            onChanged: (value) {
                              setState(() {
                                _selectedDebtType = value!;
                              });
                            },
                            activeColor: AppTheme.redDebt,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Date Picker
            InkWell(
              onTap: _selectDate,
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'التاريخ',
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(DateFormat('yyyy-MM-dd').format(_selectedDate)),
              ),
            ),
            const SizedBox(height: 16),

            // Details
            TextFormField(
              controller: _detailsController,
              decoration: const InputDecoration(
                labelText: 'التفاصيل',
                hintText: 'أدخل تفاصيل إضافية (اختياري)',
                prefixIcon: Icon(Icons.notes),
              ),
              maxLines: 3,
              textInputAction: TextInputAction.done,
            ),
            const SizedBox(height: 32),

            // Save Button
            ElevatedButton(
              onPressed: _isLoading ? null : _saveAccount,
              child:
                  _isLoading
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : Text(
                        widget.account != null ? 'تحديث الحساب' : 'حفظ الحساب',
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _saveAccount() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedCategory == null || _selectedCurrency == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار الفئة والعملة')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final accountProvider = context.read<AccountProvider>();
      final amount = double.parse(_amountController.text);

      bool success;
      if (widget.account != null) {
        // Update existing account
        success = await accountProvider.updateAccount(
          id: widget.account!.id!,
          name: _nameController.text.trim(),
          phone: _phoneController.text.trim(),
          amount: amount,
          details: _detailsController.text.trim(),
          currencyId: _selectedCurrency!.id!,
          categoryId: _selectedCategory!.id!,
          debtType: _selectedDebtType,
          date: _selectedDate,
        );
      } else {
        // Add new account
        success = await accountProvider.addAccount(
          name: _nameController.text.trim(),
          phone: _phoneController.text.trim(),
          amount: amount,
          details: _detailsController.text.trim(),
          currencyId: _selectedCurrency!.id!,
          categoryId: _selectedCategory!.id!,
          debtType: _selectedDebtType,
          date: _selectedDate,
        );
      }

      if (mounted) {
        if (success) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.account != null
                    ? 'تم تحديث الحساب بنجاح'
                    : 'تم إضافة الحساب بنجاح',
              ),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(accountProvider.error ?? 'حدث خطأ غير متوقع'),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ: ${e.toString()}')));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text('هل أنت متأكد من حذف هذا الحساب؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _deleteAccount();
                },
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  Future<void> _deleteAccount() async {
    if (widget.account == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final accountProvider = context.read<AccountProvider>();
      final success = await accountProvider.deleteAccount(widget.account!.id!);

      if (mounted) {
        if (success) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('تم حذف الحساب بنجاح')));
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(accountProvider.error ?? 'فشل في حذف الحساب'),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ: ${e.toString()}')));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
