import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/category_provider.dart';
import '../providers/currency_provider.dart';
import '../constants/app_theme.dart';
import '../models/category.dart';
import '../models/currency.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الفئات'),
            Tab(text: 'العملات'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _CategoriesTab(),
          _CurrenciesTab(),
        ],
      ),
    );
  }
}

class _CategoriesTab extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<CategoryProvider>(
      builder: (context, categoryProvider, child) {
        if (categoryProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final categories = categoryProvider.categories;

        return Column(
          children: [
            // Add Category Button
            Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _showAddCategoryDialog(context),
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة فئة جديدة'),
                ),
              ),
            ),

            // Categories List
            Expanded(
              child: categories.isEmpty
                  ? const Center(
                      child: Text('لا توجد فئات'),
                    )
                  : ListView.builder(
                      itemCount: categories.length,
                      itemBuilder: (context, index) {
                        final category = categories[index];
                        final isDefault = category.name == 'عام';
                        
                        return Card(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 4,
                          ),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: AppTheme.primaryPurple.withOpacity(0.1),
                              child: const Icon(
                                Icons.category,
                                color: AppTheme.primaryPurple,
                              ),
                            ),
                            title: Text(category.name),
                            subtitle: Text(
                              isDefault 
                                  ? 'الفئة الافتراضية' 
                                  : 'تم الإنشاء: ${category.createdAt.toString().split(' ')[0]}',
                            ),
                            trailing: isDefault
                                ? const Icon(Icons.lock, color: Colors.grey)
                                : PopupMenuButton<String>(
                                    onSelected: (value) {
                                      if (value == 'edit') {
                                        _showEditCategoryDialog(context, category);
                                      } else if (value == 'delete') {
                                        _showDeleteCategoryDialog(context, category);
                                      }
                                    },
                                    itemBuilder: (context) => [
                                      const PopupMenuItem(
                                        value: 'edit',
                                        child: Text('تعديل'),
                                      ),
                                      const PopupMenuItem(
                                        value: 'delete',
                                        child: Text('حذف'),
                                      ),
                                    ],
                                  ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        );
      },
    );
  }

  void _showAddCategoryDialog(BuildContext context) {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة فئة جديدة'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'اسم الفئة',
            hintText: 'أدخل اسم الفئة',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final name = controller.text.trim();
              if (name.isNotEmpty) {
                final success = await context.read<CategoryProvider>().addCategory(name);
                Navigator.of(context).pop();
                if (!success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(context.read<CategoryProvider>().error ?? 'فشل في إضافة الفئة'),
                    ),
                  );
                }
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _showEditCategoryDialog(BuildContext context, Category category) {
    final controller = TextEditingController(text: category.name);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل الفئة'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'اسم الفئة',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final name = controller.text.trim();
              if (name.isNotEmpty && name != category.name) {
                final success = await context.read<CategoryProvider>()
                    .updateCategory(category.id!, name);
                Navigator.of(context).pop();
                if (!success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(context.read<CategoryProvider>().error ?? 'فشل في تحديث الفئة'),
                    ),
                  );
                }
              } else {
                Navigator.of(context).pop();
              }
            },
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  void _showDeleteCategoryDialog(BuildContext context, Category category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف فئة "${category.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final success = await context.read<CategoryProvider>()
                  .deleteCategory(category.id!);
              Navigator.of(context).pop();
              if (!success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(context.read<CategoryProvider>().error ?? 'فشل في حذف الفئة'),
                  ),
                );
              }
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}

class _CurrenciesTab extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<CurrencyProvider>(
      builder: (context, currencyProvider, child) {
        if (currencyProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final currencies = currencyProvider.currencies;

        return Column(
          children: [
            // Add Currency Button
            Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _showAddCurrencyDialog(context),
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة عملة جديدة'),
                ),
              ),
            ),

            // Currencies List
            Expanded(
              child: currencies.isEmpty
                  ? const Center(
                      child: Text('لا توجد عملات'),
                    )
                  : ListView.builder(
                      itemCount: currencies.length,
                      itemBuilder: (context, index) {
                        final currency = currencies[index];
                        final defaultCurrencies = ['ريال سعودي', 'دولار أمريكي', 'يورو', 'جنيه مصري'];
                        final isDefault = defaultCurrencies.contains(currency.name);
                        
                        return Card(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 4,
                          ),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: AppTheme.primaryPurple.withOpacity(0.1),
                              child: Text(
                                currency.symbol,
                                style: const TextStyle(
                                  color: AppTheme.primaryPurple,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            title: Text(currency.name),
                            subtitle: Text(
                              isDefault 
                                  ? 'عملة افتراضية' 
                                  : 'تم الإنشاء: ${currency.createdAt.toString().split(' ')[0]}',
                            ),
                            trailing: isDefault
                                ? const Icon(Icons.lock, color: Colors.grey)
                                : PopupMenuButton<String>(
                                    onSelected: (value) {
                                      if (value == 'edit') {
                                        _showEditCurrencyDialog(context, currency);
                                      } else if (value == 'delete') {
                                        _showDeleteCurrencyDialog(context, currency);
                                      }
                                    },
                                    itemBuilder: (context) => [
                                      const PopupMenuItem(
                                        value: 'edit',
                                        child: Text('تعديل'),
                                      ),
                                      const PopupMenuItem(
                                        value: 'delete',
                                        child: Text('حذف'),
                                      ),
                                    ],
                                  ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        );
      },
    );
  }

  void _showAddCurrencyDialog(BuildContext context) {
    final nameController = TextEditingController();
    final symbolController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة عملة جديدة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم العملة',
                hintText: 'مثال: الدرهم الإماراتي',
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: symbolController,
              decoration: const InputDecoration(
                labelText: 'رمز العملة',
                hintText: 'مثال: د.إ',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final name = nameController.text.trim();
              final symbol = symbolController.text.trim();
              if (name.isNotEmpty && symbol.isNotEmpty) {
                final success = await context.read<CurrencyProvider>()
                    .addCurrency(name, symbol);
                Navigator.of(context).pop();
                if (!success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(context.read<CurrencyProvider>().error ?? 'فشل في إضافة العملة'),
                    ),
                  );
                }
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _showEditCurrencyDialog(BuildContext context, Currency currency) {
    final nameController = TextEditingController(text: currency.name);
    final symbolController = TextEditingController(text: currency.symbol);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل العملة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم العملة',
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: symbolController,
              decoration: const InputDecoration(
                labelText: 'رمز العملة',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final name = nameController.text.trim();
              final symbol = symbolController.text.trim();
              if (name.isNotEmpty && symbol.isNotEmpty && 
                  (name != currency.name || symbol != currency.symbol)) {
                final success = await context.read<CurrencyProvider>()
                    .updateCurrency(currency.id!, name, symbol);
                Navigator.of(context).pop();
                if (!success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(context.read<CurrencyProvider>().error ?? 'فشل في تحديث العملة'),
                    ),
                  );
                }
              } else {
                Navigator.of(context).pop();
              }
            },
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  void _showDeleteCurrencyDialog(BuildContext context, Currency currency) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف عملة "${currency.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final success = await context.read<CurrencyProvider>()
                  .deleteCurrency(currency.id!);
              Navigator.of(context).pop();
              if (!success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(context.read<CurrencyProvider>().error ?? 'فشل في حذف العملة'),
                  ),
                );
              }
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
