import 'package:flutter/foundation.dart';
import '../models/account.dart';
import '../models/category.dart';
import '../models/currency.dart';
import '../database/database_helper.dart';

class AccountProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Account> _accounts = [];
  bool _isLoading = false;
  String? _error;

  List<Account> get accounts => _accounts;
  bool get isLoading => _isLoading;
  String? get error => _error;

  AccountProvider() {
    loadAccounts();
  }

  Future<void> loadAccounts() async {
    _setLoading(true);
    try {
      _accounts = await _databaseHelper.getAccounts();
      _error = null;
    } catch (e) {
      _error = 'خطأ في تحميل الحسابات: ${e.toString()}';
      debugPrint('Error loading accounts: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> addAccount({
    required String name,
    required String phone,
    required double amount,
    required String details,
    required int currencyId,
    required int categoryId,
    required DebtType debtType,
    required DateTime date,
  }) async {
    if (name.trim().isEmpty) {
      _error = 'اسم الحساب مطلوب';
      notifyListeners();
      return false;
    }

    if (amount <= 0) {
      _error = 'المبلغ يجب أن يكون أكبر من صفر';
      notifyListeners();
      return false;
    }

    _setLoading(true);
    try {
      final account = Account(
        name: name.trim(),
        phone: phone.trim(),
        amount: amount,
        details: details.trim(),
        currencyId: currencyId,
        categoryId: categoryId,
        debtType: debtType,
        date: date,
        createdAt: DateTime.now(),
      );

      final id = await _databaseHelper.insertAccount(account);
      if (id > 0) {
        await loadAccounts(); // Reload to get the new account with ID
        _error = null;
        return true;
      } else {
        _error = 'فشل في إضافة الحساب';
        return false;
      }
    } catch (e) {
      _error = 'خطأ في إضافة الحساب: ${e.toString()}';
      debugPrint('Error adding account: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateAccount({
    required int id,
    required String name,
    required String phone,
    required double amount,
    required String details,
    required int currencyId,
    required int categoryId,
    required DebtType debtType,
    required DateTime date,
  }) async {
    if (name.trim().isEmpty) {
      _error = 'اسم الحساب مطلوب';
      notifyListeners();
      return false;
    }

    if (amount <= 0) {
      _error = 'المبلغ يجب أن يكون أكبر من صفر';
      notifyListeners();
      return false;
    }

    _setLoading(true);
    try {
      final existingAccount = _accounts.firstWhere((account) => account.id == id);
      final updatedAccount = existingAccount.copyWith(
        name: name.trim(),
        phone: phone.trim(),
        amount: amount,
        details: details.trim(),
        currencyId: currencyId,
        categoryId: categoryId,
        debtType: debtType,
        date: date,
      );

      final result = await _databaseHelper.updateAccount(updatedAccount);
      if (result > 0) {
        await loadAccounts(); // Reload to reflect changes
        _error = null;
        return true;
      } else {
        _error = 'فشل في تحديث الحساب';
        return false;
      }
    } catch (e) {
      _error = 'خطأ في تحديث الحساب: ${e.toString()}';
      debugPrint('Error updating account: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteAccount(int id) async {
    _setLoading(true);
    try {
      final result = await _databaseHelper.deleteAccount(id);
      if (result > 0) {
        await loadAccounts(); // Reload to reflect changes
        _error = null;
        return true;
      } else {
        _error = 'فشل في حذف الحساب';
        return false;
      }
    } catch (e) {
      _error = 'خطأ في حذف الحساب: ${e.toString()}';
      debugPrint('Error deleting account: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  List<Account> getAccountsByCategory(int categoryId) {
    return _accounts.where((account) => account.categoryId == categoryId).toList();
  }

  List<Account> getAccountsByCurrency(int currencyId) {
    return _accounts.where((account) => account.currencyId == currencyId).toList();
  }

  List<Account> getAccountsByCategoryAndCurrency(int categoryId, int currencyId) {
    return _accounts.where((account) => 
        account.categoryId == categoryId && account.currencyId == currencyId).toList();
  }

  Account? getAccountById(int id) {
    try {
      return _accounts.firstWhere((account) => account.id == id);
    } catch (e) {
      return null;
    }
  }

  Map<String, double> getCategorySummary(int categoryId) {
    final categoryAccounts = getAccountsByCategory(categoryId);
    double owedToMe = 0.0;
    double iOwe = 0.0;

    for (final account in categoryAccounts) {
      if (account.debtType == DebtType.owedToMe) {
        owedToMe += account.amount;
      } else {
        iOwe += account.amount;
      }
    }

    return {
      'owedToMe': owedToMe,
      'iOwe': iOwe,
      'balance': owedToMe - iOwe,
    };
  }

  Map<String, double> getCurrencySummary(int currencyId) {
    final currencyAccounts = getAccountsByCurrency(currencyId);
    double owedToMe = 0.0;
    double iOwe = 0.0;

    for (final account in currencyAccounts) {
      if (account.debtType == DebtType.owedToMe) {
        owedToMe += account.amount;
      } else {
        iOwe += account.amount;
      }
    }

    return {
      'owedToMe': owedToMe,
      'iOwe': iOwe,
      'balance': owedToMe - iOwe,
    };
  }

  Map<String, double> getCategoryAndCurrencySummary(int categoryId, int currencyId) {
    final accounts = getAccountsByCategoryAndCurrency(categoryId, currencyId);
    double owedToMe = 0.0;
    double iOwe = 0.0;

    for (final account in accounts) {
      if (account.debtType == DebtType.owedToMe) {
        owedToMe += account.amount;
      } else {
        iOwe += account.amount;
      }
    }

    return {
      'owedToMe': owedToMe,
      'iOwe': iOwe,
      'balance': owedToMe - iOwe,
    };
  }

  Map<String, double> getOverallSummary() {
    double owedToMe = 0.0;
    double iOwe = 0.0;

    for (final account in _accounts) {
      if (account.debtType == DebtType.owedToMe) {
        owedToMe += account.amount;
      } else {
        iOwe += account.amount;
      }
    }

    return {
      'owedToMe': owedToMe,
      'iOwe': iOwe,
      'balance': owedToMe - iOwe,
    };
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
