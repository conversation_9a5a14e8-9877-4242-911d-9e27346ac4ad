import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/account.dart';
import '../models/category.dart';
import '../models/currency.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'account_book.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create categories table
    await db.execute('''
      CREATE TABLE categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        created_at TEXT NOT NULL
      )
    ''');

    // Create currencies table
    await db.execute('''
      CREATE TABLE currencies (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        symbol TEXT NOT NULL,
        created_at TEXT NOT NULL
      )
    ''');

    // Create accounts table
    await db.execute('''
      CREATE TABLE accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT NOT NULL,
        amount REAL NOT NULL,
        details TEXT,
        currency_id INTEGER NOT NULL,
        category_id INTEGER NOT NULL,
        debt_type TEXT NOT NULL,
        date TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (currency_id) REFERENCES currencies (id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE
      )
    ''');

    // Insert default data
    await _insertDefaultData(db);
  }

  Future<void> _insertDefaultData(Database db) async {
    // Insert default category
    await db.insert('categories', {
      'name': 'عام',
      'created_at': DateTime.now().toIso8601String(),
    });

    // Insert default currencies
    final defaultCurrencies = [
      {'name': 'ريال سعودي', 'symbol': 'ر.س'},
      {'name': 'دولار أمريكي', 'symbol': '\$'},
      {'name': 'يورو', 'symbol': '€'},
      {'name': 'جنيه مصري', 'symbol': 'ج.م'},
    ];

    for (var currency in defaultCurrencies) {
      await db.insert('currencies', {
        'name': currency['name'],
        'symbol': currency['symbol'],
        'created_at': DateTime.now().toIso8601String(),
      });
    }
  }

  // Category CRUD operations
  Future<int> insertCategory(Category category) async {
    final db = await database;
    return await db.insert('categories', category.toMap());
  }

  Future<List<Category>> getCategories() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'categories',
      orderBy: 'created_at ASC',
    );
    return List.generate(maps.length, (i) => Category.fromMap(maps[i]));
  }

  Future<Category?> getCategoryById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'categories',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Category.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateCategory(Category category) async {
    final db = await database;
    return await db.update(
      'categories',
      category.toMap(),
      where: 'id = ?',
      whereArgs: [category.id],
    );
  }

  Future<int> deleteCategory(int id) async {
    final db = await database;
    return await db.delete(
      'categories',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Currency CRUD operations
  Future<int> insertCurrency(Currency currency) async {
    final db = await database;
    return await db.insert('currencies', currency.toMap());
  }

  Future<List<Currency>> getCurrencies() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'currencies',
      orderBy: 'created_at ASC',
    );
    return List.generate(maps.length, (i) => Currency.fromMap(maps[i]));
  }

  Future<Currency?> getCurrencyById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'currencies',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Currency.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateCurrency(Currency currency) async {
    final db = await database;
    return await db.update(
      'currencies',
      currency.toMap(),
      where: 'id = ?',
      whereArgs: [currency.id],
    );
  }

  Future<int> deleteCurrency(int id) async {
    final db = await database;
    return await db.delete(
      'currencies',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Account CRUD operations
  Future<int> insertAccount(Account account) async {
    final db = await database;
    return await db.insert('accounts', account.toMap());
  }

  Future<List<Account>> getAccounts() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'accounts',
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Account.fromMap(maps[i]));
  }

  Future<List<Account>> getAccountsByCategory(int categoryId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'accounts',
      where: 'category_id = ?',
      whereArgs: [categoryId],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Account.fromMap(maps[i]));
  }

  Future<List<Account>> getAccountsByCurrency(int currencyId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'accounts',
      where: 'currency_id = ?',
      whereArgs: [currencyId],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Account.fromMap(maps[i]));
  }

  Future<List<Account>> getAccountsByCategoryAndCurrency(
      int categoryId, int currencyId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'accounts',
      where: 'category_id = ? AND currency_id = ?',
      whereArgs: [categoryId, currencyId],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Account.fromMap(maps[i]));
  }

  Future<Account?> getAccountById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'accounts',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Account.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateAccount(Account account) async {
    final db = await database;
    return await db.update(
      'accounts',
      account.toMap(),
      where: 'id = ?',
      whereArgs: [account.id],
    );
  }

  Future<int> deleteAccount(int id) async {
    final db = await database;
    return await db.delete(
      'accounts',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Summary calculations
  Future<Map<String, double>> getCategorySummary(int categoryId) async {
    final db = await database;
    final List<Map<String, dynamic>> owedToMe = await db.rawQuery('''
      SELECT SUM(amount) as total FROM accounts 
      WHERE category_id = ? AND debt_type = 'owed_to_me'
    ''', [categoryId]);

    final List<Map<String, dynamic>> iOwe = await db.rawQuery('''
      SELECT SUM(amount) as total FROM accounts 
      WHERE category_id = ? AND debt_type = 'i_owe'
    ''', [categoryId]);

    return {
      'owedToMe': owedToMe.first['total']?.toDouble() ?? 0.0,
      'iOwe': iOwe.first['total']?.toDouble() ?? 0.0,
    };
  }

  Future<Map<String, double>> getCurrencySummary(int currencyId) async {
    final db = await database;
    final List<Map<String, dynamic>> owedToMe = await db.rawQuery('''
      SELECT SUM(amount) as total FROM accounts 
      WHERE currency_id = ? AND debt_type = 'owed_to_me'
    ''', [currencyId]);

    final List<Map<String, dynamic>> iOwe = await db.rawQuery('''
      SELECT SUM(amount) as total FROM accounts 
      WHERE currency_id = ? AND debt_type = 'i_owe'
    ''', [currencyId]);

    return {
      'owedToMe': owedToMe.first['total']?.toDouble() ?? 0.0,
      'iOwe': iOwe.first['total']?.toDouble() ?? 0.0,
    };
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
